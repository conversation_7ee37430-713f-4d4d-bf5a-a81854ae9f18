// src/app/thank-you/page.tsx
'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { getPlanById } from '@/lib/stripe/plans';
import Link from 'next/link';

type UserStatus = 'checking' | 'ready' | 'error' | 'timeout' | 'webhook_failed';

interface AccountInfo {
  id: string;
  email: string;
  plan: string;
  paymentVerified: boolean;
  hasTemporaryPassword: boolean;
  loginUrl: string;
}

function ThankYouContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const planId = searchParams.get('plan') || 'free';
  const sessionId = searchParams.get('session_id');

  const [userStatus, setUserStatus] = useState<UserStatus>('checking');
  const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [isReactivating, setIsReactivating] = useState(false);

  const plan = getPlanById(planId);

  // Verificación de estado del usuario
  useEffect(() => {
    if (!sessionId) {
      setUserStatus('error');
      return;
    }

    const checkAccountStatus = async () => {
      try {
        const response = await fetch(`/api/user/status?session_id=${sessionId}`);
        const data = await response.json();

        if (data.ready) {
          setUserStatus('ready');
          setAccountInfo(data.user);

          // Redirección automática tras 3 segundos
          setTimeout(() => {
            router.push(`/welcome?plan=${planId}&new_user=true`);
          }, 3000);
        } else if (timeElapsed >= 90) {
          // Timeout inteligente para detectar webhook fallido
          setUserStatus('webhook_failed');
        } else if (timeElapsed >= 60) {
          setUserStatus('timeout');
        }
      } catch (error) {
        console.error('Error verificando estado:', error);
        if (timeElapsed >= 30) {
          setUserStatus('error');
        }
      }
    };

    // Verificación inicial inmediata
    checkAccountStatus();

    // Polling cada 3 segundos
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 3);
      if (userStatus === 'checking') {
        checkAccountStatus();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [sessionId, userStatus, timeElapsed, planId, router]);

  // Función para reactivar cuenta
  const handleReactivateAccount = async () => {
    setIsReactivating(true);
    try {
      const response = await fetch('/api/user/reactivate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      });

      if (response.ok) {
        setUserStatus('checking');
        setTimeElapsed(0);
      } else {
        console.error('Error reactivando cuenta');
      }
    } catch (error) {
      console.error('Error reactivando cuenta:', error);
    } finally {
      setIsReactivating(false);
    }
  };

  // Estado: Verificando
  if (userStatus === 'checking') {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>

              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">
                ¡Pago Confirmado!
              </h2>

              <p className="text-lg text-gray-700 mb-6">
                Tu cuenta para el <strong>{plan?.name}</strong> se está creando automáticamente
              </p>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <p className="text-blue-800 text-sm">
                  <strong>🤖 Proceso automático en curso</strong><br/>
                  No necesitas hacer nada más. Tu cuenta estará lista en unos segundos.
                </p>
              </div>

              <p className="text-sm text-gray-600">
                Tiempo transcurrido: {timeElapsed} segundos
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Estado: Listo
  if (userStatus === 'ready' && accountInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>

              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">
                ¡Cuenta Creada Exitosamente!
              </h2>

              <p className="text-lg text-gray-700 mb-6">
                Tu cuenta está lista para usar
              </p>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-green-800 mb-2">¡Tu cuenta está lista!</h3>
                <p className="text-green-700 text-sm mb-3">
                  <strong>Email de acceso:</strong> {accountInfo.email}
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <p className="text-blue-800 text-xs">
                    <strong>📧 Revisa tu email</strong><br/>
                    Te hemos enviado un enlace para establecer tu contraseña.
                    Revisa tu bandeja de entrada (y spam) para completar la configuración de tu cuenta.
                  </p>
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Redirigiendo automáticamente en 3 segundos...
              </p>

              <Link
                href="/welcome"
                className="inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                Acceder Ahora
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Estados: Error, Timeout, Webhook Fallido
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
        <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 mb-4">
              {userStatus === 'webhook_failed' ? 'Reactivación Necesaria' :
               userStatus === 'timeout' ? 'Procesando...' : 'Problema Temporal'}
            </h2>

            <p className="text-gray-700 mb-6">
              {userStatus === 'webhook_failed'
                ? 'Tu pago fue procesado, pero necesitamos reactivar la creación de tu cuenta.'
                : userStatus === 'timeout'
                  ? 'Tu cuenta se está creando. Esto puede tardar un poco más de lo normal.'
                  : 'Hubo un problema al verificar tu cuenta.'
              }
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <p className="text-blue-800 text-sm">
                <strong>Tu pago fue procesado correctamente.</strong><br/>
                {userStatus === 'webhook_failed'
                  ? 'Haz clic en "Reactivar Cuenta" para completar el proceso.'
                  : 'Si el problema persiste, contacta con soporte.'
                }
              </p>
            </div>

            <div className="space-y-3">
              {userStatus === 'webhook_failed' && (
                <button
                  onClick={handleReactivateAccount}
                  disabled={isReactivating}
                  className="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {isReactivating ? 'Reactivando...' : 'Reactivar Cuenta'}
                </button>
              )}

              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Verificar Nuevamente
              </button>

              <a
                href={`mailto:<EMAIL>?subject=Problema con cuenta&body=ID de Sesión: ${sessionId}`}
                className="block w-full bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors text-center"
              >
                Contactar Soporte
              </a>

              {sessionId && (
                <div className="mt-4 text-xs text-gray-500">
                  ID de Sesión: {sessionId}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ThankYouPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Cargando...</p>
            </div>
          </div>
        </div>
      </div>
    }>
      <ThankYouContent />
    </Suspense>
  );
}
