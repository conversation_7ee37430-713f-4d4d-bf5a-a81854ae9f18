// src/components/ui/FreeAccountTimer.tsx
// Componente para mostrar contador de tiempo restante de cuenta gratuita

'use client';

import React from 'react';
import Link from 'next/link';
import { FiClock, FiUpgrade } from 'react-icons/fi';
import { useFreeAccount, useTimeRemaining } from '@/hooks/useFreeAccount';

interface FreeAccountTimerProps {
  className?: string;
  showUpgradeButton?: boolean;
  variant?: 'default' | 'compact' | 'badge';
}

export default function FreeAccountTimer({ 
  className = '', 
  showUpgradeButton = true,
  variant = 'default'
}: FreeAccountTimerProps) {
  const { isFreeAccount, status } = useFreeAccount();
  const timeRemaining = useTimeRemaining(status?.expiresAt || null);

  // No mostrar si no es cuenta gratuita
  if (!isFreeAccount || !status) {
    return null;
  }

  // Variante badge (para header)
  if (variant === 'badge') {
    return (
      <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium ${
        timeRemaining.isExpired 
          ? 'bg-red-100 text-red-800'
          : timeRemaining.days <= 1 
            ? 'bg-orange-100 text-orange-800'
            : 'bg-blue-100 text-blue-800'
      } ${className}`}>
        <FiClock className="w-3 h-3" />
        <span>
          {timeRemaining.isExpired ? 'Expirado' : timeRemaining.formatted}
        </span>
        {showUpgradeButton && (
          <Link
            href="/payment"
            className="ml-2 text-blue-600 hover:text-blue-800 underline"
          >
            Actualizar
          </Link>
        )}
      </div>
    );
  }

  // Variante compacta
  if (variant === 'compact') {
    return (
      <div className={`flex items-center justify-between p-3 rounded-lg border ${
        timeRemaining.isExpired 
          ? 'bg-red-50 border-red-200'
          : timeRemaining.days <= 1 
            ? 'bg-orange-50 border-orange-200'
            : 'bg-blue-50 border-blue-200'
      } ${className}`}>
        <div className="flex items-center space-x-2">
          <FiClock className={`w-4 h-4 ${
            timeRemaining.isExpired 
              ? 'text-red-500'
              : timeRemaining.days <= 1 
                ? 'text-orange-500'
                : 'text-blue-500'
          }`} />
          <span className={`text-sm font-medium ${
            timeRemaining.isExpired 
              ? 'text-red-800'
              : timeRemaining.days <= 1 
                ? 'text-orange-800'
                : 'text-blue-800'
          }`}>
            {timeRemaining.isExpired 
              ? 'Cuenta expirada'
              : `${timeRemaining.formatted} restantes`
            }
          </span>
        </div>
        
        {showUpgradeButton && (
          <Link
            href="/payment"
            className={`text-xs px-2 py-1 rounded font-medium transition-colors ${
              timeRemaining.isExpired
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            Actualizar
          </Link>
        )}
      </div>
    );
  }

  // Variante por defecto (completa)
  return (
    <div className={`p-6 rounded-xl border shadow-sm ${
      timeRemaining.isExpired 
        ? 'bg-red-50 border-red-200'
        : timeRemaining.days <= 1 
          ? 'bg-orange-50 border-orange-200'
          : 'bg-blue-50 border-blue-200'
    } ${className}`}>
      <div className="text-center">
        {/* Icono principal */}
        <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
          timeRemaining.isExpired 
            ? 'bg-red-100'
            : timeRemaining.days <= 1 
              ? 'bg-orange-100'
              : 'bg-blue-100'
        }`}>
          <FiClock className={`w-8 h-8 ${
            timeRemaining.isExpired 
              ? 'text-red-500'
              : timeRemaining.days <= 1 
                ? 'text-orange-500'
                : 'text-blue-500'
          }`} />
        </div>

        {/* Título */}
        <h3 className={`text-xl font-bold mb-2 ${
          timeRemaining.isExpired 
            ? 'text-red-800'
            : timeRemaining.days <= 1 
              ? 'text-orange-800'
              : 'text-blue-800'
        }`}>
          {timeRemaining.isExpired 
            ? 'Cuenta Expirada'
            : 'Tiempo Restante'
          }
        </h3>

        {/* Tiempo restante */}
        {!timeRemaining.isExpired ? (
          <div className="mb-4">
            <div className={`text-3xl font-bold mb-1 ${
              timeRemaining.days <= 1 ? 'text-orange-600' : 'text-blue-600'
            }`}>
              {timeRemaining.formatted}
            </div>
            <p className={`text-sm ${
              timeRemaining.days <= 1 ? 'text-orange-600' : 'text-blue-600'
            }`}>
              {timeRemaining.days > 0 
                ? `${timeRemaining.days} día${timeRemaining.days !== 1 ? 's' : ''} y ${timeRemaining.hours} hora${timeRemaining.hours !== 1 ? 's' : ''}`
                : timeRemaining.hours > 0
                  ? `${timeRemaining.hours} hora${timeRemaining.hours !== 1 ? 's' : ''} y ${timeRemaining.minutes} minuto${timeRemaining.minutes !== 1 ? 's' : ''}`
                  : `${timeRemaining.minutes} minuto${timeRemaining.minutes !== 1 ? 's' : ''}`
              }
            </p>
          </div>
        ) : (
          <div className="mb-4">
            <div className="text-3xl font-bold mb-1 text-red-600">
              00:00:00
            </div>
            <p className="text-sm text-red-600">
              Tu período gratuito ha terminado
            </p>
          </div>
        )}

        {/* Barra de progreso */}
        {!timeRemaining.isExpired && (
          <div className="mb-4">
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div 
                className={`h-2 rounded-full transition-all duration-1000 ${
                  status.progressPercentage >= 80 
                    ? 'bg-red-500' 
                    : status.progressPercentage >= 60 
                      ? 'bg-orange-500'
                      : 'bg-blue-500'
                }`}
                style={{ width: `${Math.min(100, status.progressPercentage)}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>Inicio</span>
              <span>{Math.round(status.progressPercentage)}% usado</span>
              <span>5 días</span>
            </div>
          </div>
        )}

        {/* Mensaje */}
        <p className={`text-sm mb-4 ${
          timeRemaining.isExpired 
            ? 'text-red-600'
            : timeRemaining.days <= 1 
              ? 'text-orange-600'
              : 'text-blue-600'
        }`}>
          {timeRemaining.isExpired 
            ? 'Actualiza tu plan para continuar usando OposiAI'
            : timeRemaining.days <= 1
              ? '¡Tu cuenta expira pronto! Considera actualizar tu plan'
              : 'Aprovecha al máximo tu período de prueba gratuito'
          }
        </p>

        {/* Botón de actualización */}
        {showUpgradeButton && (
          <Link
            href="/payment"
            className={`inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-colors ${
              timeRemaining.isExpired
                ? 'bg-red-600 text-white hover:bg-red-700'
                : timeRemaining.days <= 1
                  ? 'bg-orange-600 text-white hover:bg-orange-700'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            <FiUpgrade className="w-5 h-5 mr-2" />
            {timeRemaining.isExpired ? 'Renovar Ahora' : 'Actualizar Plan'}
          </Link>
        )}

        {/* Información adicional */}
        {!timeRemaining.isExpired && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Tu cuenta gratuita incluye acceso limitado a todas las funcionalidades.
              <br />
              Actualiza para obtener acceso completo sin restricciones.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
