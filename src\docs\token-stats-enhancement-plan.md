# Plan de implementación: Mejora de estadísticas de tokens

## 1. Modificaciones en el backend

### 1.1 Actualizar `tokenUsageService.ts`

- Crear nueva función `getTokenUsageProgress` que devuelva:
  - Porcentaje de uso actual
  - Límite de tokens según plan
  - Tokens usados
  - Tokens restantes
  - Historial de uso diario (para gráfico progresivo)

### 1.2 Crear endpoint para compra de tokens adicionales

- Crear ruta `/api/tokens/purchase`
- Implementar integración con pasarela de pago (Stripe)
- Actualizar límite de tokens tras pago exitoso

## 2. Modificaciones en el frontend

### 2.1 Crear componente `TokenProgressBar.tsx`

- Implementar barra de progreso visual
- Mostrar porcentaje de uso
- Cambiar color según nivel de uso (verde < 50%, amarillo < 80%, rojo > 80%)
- Incluir texto con tokens usados/límite

### 2.2 Crear componente `TokenUsageChart.tsx`

- Implementar gráfico de línea con uso progresivo
- Utilizar biblioteca Chart.js o similar
- Mostrar tendencia de uso en los últimos 30 días

### 2.3 Crear componente `TokenPurchaseButton.tsx`

- Botón para comprar tokens adicionales
- Modal de confirmación con detalles de precio
- Integración con pasarela de pago
- Mensaje de éxito/error tras la transacción

### 2.4 Actualizar `TokenStatsModal.tsx`

- Integrar nuevos componentes
- Condicionar visualización según plan del usuario
- Añadir sección de historial de compras de tokens

## 3. Lógica de negocio

### 3.1 Reglas de visualización

- Si `user.subscription_plan === 'free'`, no mostrar estadísticas avanzadas
- Para planes 'usuario' y 'pro', mostrar todas las estadísticas
- Mostrar botón de compra solo para usuarios con planes de pago verificados

### 3.2 Lógica de compra de tokens

- Precio: 10€ por 1 millón de tokens
- Actualizar `monthly_token_limit` en la tabla `user_profiles`
- Registrar transacción en nueva tabla `token_purchases`

## 4. Pruebas

- Pruebas unitarias para nuevas funciones
- Pruebas de integración para flujo de compra
- Pruebas de UI para componentes visuales

## 5. Despliegue

- Implementar cambios en entorno de desarrollo
- Validar funcionamiento
- Desplegar a producción