# PLAN DE AUTOMATIZACIÓN - REGISTRO PLAN GRATUITO

## ANÁLISIS INICIAL

### Estado Actual del Sistema
- **Plan Gratuito**: Proceso completamente manual
- **Planes de Pago**: Automatizados vía Stripe + Webhooks
- **Problema**: Inconsistencia en flujos de registro
- **Impacto**: Experiencia de usuario deficiente y falta de escalabilidad

### Endpoint Existente Análisis
**¿Se puede usar `/api/stripe/create-checkout-session` para plan gratuito?**
- ❌ **NO RECOMENDADO**: Endpoint específico para Stripe
- ❌ Requiere configuración de precios en Stripe
- ❌ Plan gratuito no necesita procesamiento de pagos
- ✅ **RECOMENDACIÓN**: Crear endpoint específico `/api/auth/register-free`

### Flujo Actual vs Propuesto

#### Flujo Actual (Manual)
```
Usuario → Formulario → Notificación Email → Administrador Manual → Usuario Espera
```

#### Flujo Propuesto (Automatizado)
```
Usuario → Formulario → Creación Automática → Email Bienvenida → Acceso Inmediato
```

---

## FASE 1: PREPARACIÓN Y ANÁLISIS

### 1.1 Errores Identificados
- [ ] Falta de automatización en creación de usuarios gratuitos
- [ ] Dependencia crítica de intervención manual
- [ ] No hay validación de límites de tiempo (5 días)
- [ ] Ausencia de sistema de expiración automática
- [ ] Inconsistencia entre flujos de registro
- [ ] Falta de validación de emails duplicados para plan gratuito

### 1.2 Requisitos Técnicos
- [ ] Endpoint específico para registro gratuito
- [ ] Sistema de expiración automática (5 días)
- [ ] Validación de límites en tiempo real
- [ ] Integración con sistema de emails existente
- [ ] Middleware de validación de cuentas expiradas
- [ ] Sistema de limpieza automática

### 1.3 Validaciones de Seguridad
- [ ] Prevenir múltiples registros con mismo email
- [ ] Rate limiting para registros gratuitos
- [ ] Validación de formato de email
- [ ] Detección de patrones de abuso
- [ ] Auditoría de registros gratuitos

---

## FASE 2: IMPLEMENTACIÓN BACKEND

### 2.1 Nuevos Endpoints
- [ ] **`POST /api/auth/register-free`**
  - Registro automático para plan gratuito
  - Validación de email único
  - Creación inmediata de usuario y perfil
  - Envío de email de bienvenida

- [ ] **`GET /api/auth/free-account-status`**
  - Verificar estado de cuenta gratuita
  - Tiempo restante de validez
  - Límites de uso actuales

- [ ] **`POST /api/admin/cleanup-expired-free`**
  - Limpieza manual de cuentas expiradas
  - Solo para administradores

### 2.2 Modificaciones en Base de Datos
- [ ] Agregar campos en `user_profiles`:
  - `free_account_expires_at: timestamp`
  - `free_account_created_at: timestamp`
  - `free_account_usage_count: jsonb`

- [ ] Crear índices para optimización:
  - Índice en `free_account_expires_at`
  - Índice compuesto en `subscription_plan + free_account_expires_at`

### 2.3 Servicios y Utilidades
- [ ] **`FreeAccountService`**
  - Creación automática de cuentas gratuitas
  - Validación de expiración
  - Gestión de límites de uso

- [ ] **`ExpirationService`**
  - Job de limpieza automática
  - Notificaciones de expiración
  - Desactivación de cuentas vencidas

---

## FASE 3: IMPLEMENTACIÓN FRONTEND

### 3.1 Modificaciones en Páginas
- [ ] **`/payment` page**
  - Detectar plan gratuito
  - Redirigir a flujo específico
  - Formulario simplificado (solo email y nombre)

- [ ] **`/thank-you` page**
  - Mensaje específico para plan gratuito
  - Información sobre límites y duración
  - Acceso directo a aplicación

- [ ] **`/welcome` page**
  - Información específica de plan gratuito
  - Contador de días restantes
  - Promoción de planes pagos

### 3.2 Componentes de UI
- [ ] **`FreeAccountTimer`**
  - Mostrar tiempo restante
  - Alertas de expiración próxima

- [ ] **`UsageLimitsIndicator`**
  - Mostrar límites actuales
  - Progreso de uso por característica

- [ ] **`UpgradePrompt`**
  - Promoción de planes pagos
  - Comparación de características

---

## FASE 4: VALIDACIÓN Y LÍMITES

### 4.1 Sistema de Límites
- [ ] **Validación en tiempo real**:
  - Documentos: máximo 1
  - Tests: máximo 10 preguntas
  - Flashcards: máximo 10 tarjetas
  - Mapas mentales: máximo 2
  - Tokens: máximo 50,000 mensuales

- [ ] **Middleware de validación**:
  - Verificar expiración en cada request
  - Bloquear acceso si cuenta expirada
  - Redirigir a página de upgrade

### 4.2 Hooks y Servicios Frontend
- [ ] **`useFreeAccountLimits`**
  - Hook para validar límites
  - Estado de cuenta gratuita
  - Tiempo restante

- [ ] **`useExpirationWarning`**
  - Notificaciones de expiración
  - Alertas automáticas

---

## FASE 5: TESTING Y VALIDACIÓN

### 5.1 Tests Unitarios
- [ ] Tests para `FreeAccountService`
- [ ] Tests para validación de límites
- [ ] Tests para sistema de expiración
- [ ] Tests para endpoints nuevos

### 5.2 Tests de Integración
- [ ] Flujo completo de registro gratuito
- [ ] Validación de expiración automática
- [ ] Limpieza de cuentas vencidas
- [ ] Integración con sistema de emails

### 5.3 Tests de Seguridad
- [ ] Prevención de registros duplicados
- [ ] Rate limiting efectivo
- [ ] Validación de permisos
- [ ] Detección de abuso

---

## FASE 6: DEPLOYMENT Y MONITOREO

### 6.1 Configuración de Producción
- [ ] Variables de entorno para plan gratuito
- [ ] Configuración de jobs de limpieza
- [ ] Configuración de emails automáticos
- [ ] Métricas y logging

### 6.2 Monitoreo y Métricas
- [ ] Dashboard de cuentas gratuitas
- [ ] Métricas de conversión
- [ ] Alertas de uso anómalo
- [ ] Reportes de expiración

---

## CHECKLIST DE IMPLEMENTACIÓN

### Backend Completado
- [ ] Endpoint `/api/auth/register-free` creado
- [ ] Servicio `FreeAccountService` implementado
- [ ] Sistema de expiración configurado
- [ ] Validaciones de seguridad implementadas
- [ ] Tests unitarios pasando

### Frontend Completado
- [ ] Flujo de registro gratuito implementado
- [ ] Componentes de UI creados
- [ ] Hooks de validación funcionando
- [ ] Páginas modificadas correctamente
- [ ] Tests de integración pasando

### Base de Datos Completada
- [ ] Campos agregados a `user_profiles`
- [ ] Índices creados y optimizados
- [ ] Migraciones ejecutadas
- [ ] Datos de prueba configurados

### Seguridad y Validación
- [ ] Rate limiting configurado
- [ ] Validaciones de email implementadas
- [ ] Sistema anti-abuso funcionando
- [ ] Auditoría configurada

---

## RESULTADO ESPERADO

### Beneficios Técnicos
- ✅ Registro automático e instantáneo
- ✅ Escalabilidad para miles de usuarios
- ✅ Consistencia entre todos los planes
- ✅ Eliminación de trabajo manual

### Beneficios de Negocio
- ✅ Mejor experiencia de usuario
- ✅ Mayor tasa de conversión
- ✅ Reducción de fricción en registro
- ✅ Métricas y analytics mejoradas

### Funcionalidades Automatizadas
- ✅ Creación instantánea de cuentas
- ✅ Validación automática de límites
- ✅ Expiración automática después de 5 días
- ✅ Limpieza automática de cuentas vencidas
- ✅ Notificaciones automáticas
- ✅ Promoción automática de upgrades

---

## NOTAS DE IMPLEMENTACIÓN

### Consideraciones Importantes
1. **Compatibilidad**: Mantener compatibilidad con sistema actual
2. **Rollback**: Plan de rollback en caso de problemas
3. **Migración**: Migrar cuentas gratuitas existentes
4. **Testing**: Testing exhaustivo antes de deployment
5. **Monitoreo**: Monitoreo continuo post-deployment

### Próximos Pasos
1. Revisar y aprobar plan
2. Implementar Fase 1 (Backend)
3. Testing y validación
4. Implementar Fase 2 (Frontend)
5. Testing de integración
6. Deployment gradual
7. Monitoreo y optimización

---

**Estado del Plan**: 📋 PENDIENTE DE IMPLEMENTACIÓN
**Fecha de Creación**: $(date)
**Última Actualización**: $(date)
