// src/lib/services/planValidation.ts
// Servicio para validación de planes y acceso a características

import { SupabaseAdminService, ExtendedUserProfile } from '@/lib/supabase/admin';
import { getPlanConfiguration, hasFeatureAccess, canPerformAction } from '@/lib/utils/planLimits';
import { WebhookLogger } from '@/lib/utils/webhookLogger';

export interface ValidationResult {
  allowed: boolean;
  reason?: string;
  remainingUsage?: number;
  planLimits?: any;
}

export interface UserAccessInfo {
  userId: string;
  plan: string;
  paymentVerified: boolean;
  features: string[];
  limits: any;
  currentUsage: any;
}

export class PlanValidationService {
  
  /**
   * Validar acceso de usuario a una característica específica
   */
  static async validateFeatureAccess(
    userId: string,
    featureName: string,
    tokensToUse: number = 0
  ): Promise<ValidationResult> {
    try {
      // Obtener perfil del usuario
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        await WebhookLogger.logFeatureAccess(
          userId,
          featureName,
          false,
          'unknown',
          0,
          'User profile not found'
        );
        
        return {
          allowed: false,
          reason: 'Perfil de usuario no encontrado'
        };
      }
      
      // Verificar pago para planes de pago
      if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
        await WebhookLogger.logFeatureAccess(
          userId,
          featureName,
          false,
          profile.subscription_plan,
          0,
          'Payment not verified'
        );
        
        return {
          allowed: false,
          reason: 'Pago no verificado. Complete el proceso de pago para acceder a esta característica.'
        };
      }
      
      // Verificar acceso a la característica según el plan
      if (!hasFeatureAccess(profile.subscription_plan, featureName)) {
        await WebhookLogger.logFeatureAccess(
          userId,
          featureName,
          false,
          profile.subscription_plan,
          0,
          `Feature not available in ${profile.subscription_plan} plan`
        );
        
        return {
          allowed: false,
          reason: `La característica ${featureName} no está disponible en su plan ${profile.subscription_plan}`
        };
      }
      
      // Verificar límites de tokens si aplica
      if (tokensToUse > 0) {
        const tokenValidation = await this.validateTokenUsage(profile, tokensToUse);
        if (!tokenValidation.allowed) {
          await WebhookLogger.logFeatureAccess(
            userId,
            featureName,
            false,
            profile.subscription_plan,
            tokensToUse,
            tokenValidation.reason
          );
          
          return tokenValidation;
        }
      }
      
      // Log de acceso exitoso
      await WebhookLogger.logFeatureAccess(
        userId,
        featureName,
        true,
        profile.subscription_plan,
        tokensToUse
      );
      
      return {
        allowed: true,
        remainingUsage: profile.monthly_token_limit - profile.current_month_tokens,
        planLimits: {
          monthlyTokens: profile.monthly_token_limit,
          currentTokens: profile.current_month_tokens
        }
      };
      
    } catch (error) {
      console.error('Error validating feature access:', error);
      
      await WebhookLogger.logFeatureAccess(
        userId,
        featureName,
        false,
        'error',
        tokensToUse,
        'Internal validation error'
      );
      
      return {
        allowed: false,
        reason: 'Error interno de validación'
      };
    }
  }
  
  /**
   * Validar uso de tokens
   */
  private static async validateTokenUsage(
    profile: ExtendedUserProfile,
    tokensToUse: number
  ): Promise<ValidationResult> {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    
    // Reset de tokens si es un nuevo mes
    if (profile.current_month !== currentMonth) {
      await SupabaseAdminService.upsertUserProfile({
        ...profile,
        current_month_tokens: 0,
        current_month: currentMonth,
        updated_at: new Date().toISOString()
      });
      
      profile.current_month_tokens = 0;
      profile.current_month = currentMonth;
    }
    
    // Verificar si tiene tokens suficientes
    const tokensAfterUse = profile.current_month_tokens + tokensToUse;
    
    if (tokensAfterUse > profile.monthly_token_limit) {
      return {
        allowed: false,
        reason: `Límite mensual de tokens alcanzado. Usado: ${profile.current_month_tokens}/${profile.monthly_token_limit}`,
        remainingUsage: Math.max(0, profile.monthly_token_limit - profile.current_month_tokens)
      };
    }
    
    return {
      allowed: true,
      remainingUsage: profile.monthly_token_limit - tokensAfterUse
    };
  }
  
  /**
   * Obtener información completa de acceso del usuario
   */
  static async getUserAccessInfo(userId: string): Promise<UserAccessInfo | null> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return null;
      }
      
      const planConfig = getPlanConfiguration(profile.subscription_plan);
      
      if (!planConfig) {
        return null;
      }
      
      return {
        userId,
        plan: profile.subscription_plan,
        paymentVerified: profile.payment_verified,
        features: planConfig.features,
        limits: planConfig.limits,
        currentUsage: {
          tokens: profile.current_month_tokens,
          tokenLimit: profile.monthly_token_limit,
          month: profile.current_month
        }
      };
      
    } catch (error) {
      console.error('Error getting user access info:', error);
      return null;
    }
  }
  
  /**
   * Verificar si el usuario puede realizar una acción específica
   */
  static async canUserPerformAction(
    userId: string,
    action: 'test_generation' | 'flashcard_generation' | 'mind_map_generation' | 'ai_chat' | 'study_planning' | 'summary_generation',
    quantity: number = 1
  ): Promise<ValidationResult> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return {
          allowed: false,
          reason: 'Usuario no encontrado'
        };
      }
      
      // Mapear acciones a características
      const actionToFeature = {
        'test_generation': 'test_generation',
        'flashcard_generation': 'flashcard_generation',
        'mind_map_generation': 'mind_map_generation',
        'ai_chat': 'ai_tutor_chat',
        'study_planning': 'study_planning',
        'summary_generation': 'summary_a1_a2'
      };
      
      const featureName = actionToFeature[action];
      
      // Verificar acceso básico a la característica
      const basicValidation = await this.validateFeatureAccess(userId, featureName);
      
      if (!basicValidation.allowed) {
        return basicValidation;
      }
      
      // Para plan gratuito, verificar límites específicos
      if (profile.subscription_plan === 'free') {
        const weeklyLimits = {
          'test_generation': 10,
          'flashcard_generation': 10,
          'mind_map_generation': 2
        };
        
        if (action in weeklyLimits) {
          // Aquí podrías implementar lógica para verificar uso semanal
          // Por ahora, asumimos que está dentro del límite
        }
      }
      
      return {
        allowed: true,
        remainingUsage: basicValidation.remainingUsage
      };
      
    } catch (error) {
      console.error('Error checking user action:', error);
      return {
        allowed: false,
        reason: 'Error interno de validación'
      };
    }
  }
  
  /**
   * Actualizar uso de tokens después de una operación
   */
  static async updateTokenUsage(
    userId: string,
    tokensUsed: number,
    activity: string
  ): Promise<boolean> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return false;
      }
      
      const newTokenCount = profile.current_month_tokens + tokensUsed;
      
      await SupabaseAdminService.upsertUserProfile({
        ...profile,
        current_month_tokens: newTokenCount,
        updated_at: new Date().toISOString()
      });
      
      console.log(`✅ Tokens actualizados para usuario ${userId}: +${tokensUsed} (Total: ${newTokenCount}/${profile.monthly_token_limit})`);
      
      return true;
      
    } catch (error) {
      console.error('Error updating token usage:', error);
      return false;
    }
  }
  
  /**
   * Verificar si el usuario necesita upgrade de plan
   */
  static async checkUpgradeNeeded(userId: string): Promise<{
    needsUpgrade: boolean;
    reason?: string;
    suggestedPlan?: string;
  }> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return { needsUpgrade: false };
      }
      
      // Verificar si está cerca del límite de tokens
      const usagePercentage = (profile.current_month_tokens / profile.monthly_token_limit) * 100;
      
      if (usagePercentage >= 90) {
        const suggestedPlan = profile.subscription_plan === 'free' ? 'usuario' : 'pro';
        
        return {
          needsUpgrade: true,
          reason: `Has usado el ${usagePercentage.toFixed(1)}% de tus tokens mensuales`,
          suggestedPlan
        };
      }
      
      return { needsUpgrade: false };
      
    } catch (error) {
      console.error('Error checking upgrade need:', error);
      return { needsUpgrade: false };
    }
  }
}
