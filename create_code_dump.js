// Script para crear un archivo txt con el código de todos los archivos fuente del proyecto
// Excluye node_modules, .next, temarios (PDFs), test/data y archivos binarios

const fs = require('fs');
const path = require('path');

const OUTPUT_FILE = 'codigo_completo_proyecto.txt';
const EXCLUDE_DIRS = ['node_modules', '.next', 'temarios', 'test/data', '.git', '.vscode'];
const INCLUDE_EXT = ['.js', '.ts', '.tsx', '.json', '.md', '.css', '.sql', '.jsx', '.html', '.yml', '.yaml'];

function isBinary(filePath) {
  const textExts = INCLUDE_EXT;
  return !textExts.includes(path.extname(filePath));
}

function shouldExclude(filePath) {
  return EXCLUDE_DIRS.some(dir => filePath.split(path.sep).includes(dir));
}

function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    if (stat.isDirectory()) {
      if (!shouldExclude(filePath)) {
        getAllFiles(filePath, fileList);
      }
    } else {
      if (!shouldExclude(filePath) && INCLUDE_EXT.includes(path.extname(file))) {
        fileList.push(filePath);
      }
    }
  });
  return fileList;
}

function main() {
  const rootDir = process.cwd();
  const allFiles = getAllFiles(rootDir);
  let output = '';
  allFiles.forEach(file => {
    output += `\n\n==============================\nArchivo: ${file.replace(rootDir + path.sep, '')}\n==============================\n`;
    output += fs.readFileSync(file, 'utf8');
  });
  fs.writeFileSync(OUTPUT_FILE, output, 'utf8');
  console.log(`Archivo generado: ${OUTPUT_FILE}`);
}

main();
