// src/lib/utils/webhookLogger.ts
// Sistema de logging y auditoría para webhooks

import { SupabaseAdminService } from '@/lib/supabase/admin';

export interface WebhookLogEntry {
  eventType: string;
  eventId: string;
  success: boolean;
  processingTime: number;
  message: string;
  error?: string;
  data?: any;
  metadata?: any;
}

export class WebhookLogger {
  
  /**
   * Log de evento de webhook procesado
   */
  static async logWebhookEvent(entry: WebhookLogEntry): Promise<void> {
    try {
      // Log en consola con formato estructurado
      const logLevel = entry.success ? '✅' : '❌';
      const timestamp = new Date().toISOString();
      
      console.log(`${logLevel} [WEBHOOK] ${timestamp}`, {
        eventType: entry.eventType,
        eventId: entry.eventId,
        success: entry.success,
        processingTime: `${entry.processingTime}ms`,
        message: entry.message,
        ...(entry.error && { error: entry.error }),
        ...(entry.data && { data: entry.data })
      });
      
      // En producción, aquí podrías enviar a servicios de logging externos
      // como DataDog, LogRocket, Sentry, etc.
      if (process.env.NODE_ENV === 'production') {
        await this.logToExternalService(entry);
      }
      
    } catch (error) {
      console.error('Error logging webhook event:', error);
    }
  }
  
  /**
   * Log de acceso a características
   */
  static async logFeatureAccess(
    userId: string,
    featureName: string,
    accessGranted: boolean,
    planAtTime: string,
    tokensUsed: number = 0,
    denialReason?: string
  ): Promise<void> {
    try {
      await SupabaseAdminService.logFeatureAccess({
        user_id: userId,
        feature_name: featureName,
        access_granted: accessGranted,
        plan_at_time: planAtTime,
        tokens_used: tokensUsed,
        denial_reason: denialReason
      });
      
      const logLevel = accessGranted ? '✅' : '❌';
      console.log(`${logLevel} [FEATURE_ACCESS]`, {
        userId,
        feature: featureName,
        granted: accessGranted,
        plan: planAtTime,
        tokens: tokensUsed,
        ...(denialReason && { reason: denialReason })
      });
      
    } catch (error) {
      console.error('Error logging feature access:', error);
    }
  }
  
  /**
   * Log de cambio de plan
   */
  static async logPlanChange(
    userId: string,
    oldPlan: string | null,
    newPlan: string,
    changedBy: 'system' | 'admin' | 'user',
    reason: string,
    transactionId?: string
  ): Promise<void> {
    try {
      await SupabaseAdminService.logPlanChange({
        user_id: userId,
        old_plan: oldPlan || undefined,
        new_plan: newPlan,
        changed_by: changedBy,
        reason,
        transaction_id: transactionId
      });
      
      console.log('🔄 [PLAN_CHANGE]', {
        userId,
        oldPlan,
        newPlan,
        changedBy,
        reason,
        transactionId
      });
      
    } catch (error) {
      console.error('Error logging plan change:', error);
    }
  }
  
  /**
   * Log de error crítico
   */
  static async logCriticalError(
    context: string,
    error: Error,
    additionalData?: any
  ): Promise<void> {
    try {
      const errorLog = {
        context,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        additionalData
      };
      
      console.error('🚨 [CRITICAL_ERROR]', errorLog);
      
      // En producción, enviar alertas inmediatas
      if (process.env.NODE_ENV === 'production') {
        await this.sendCriticalAlert(errorLog);
      }
      
    } catch (logError) {
      console.error('Error logging critical error:', logError);
    }
  }
  
  /**
   * Log de métricas de rendimiento
   */
  static logPerformanceMetrics(
    operation: string,
    duration: number,
    success: boolean,
    additionalMetrics?: Record<string, any>
  ): void {
    const metrics = {
      operation,
      duration: `${duration}ms`,
      success,
      timestamp: new Date().toISOString(),
      ...additionalMetrics
    };
    
    console.log('📊 [PERFORMANCE]', metrics);
    
    // En producción, enviar a servicio de métricas
    if (process.env.NODE_ENV === 'production') {
      this.sendMetrics(metrics);
    }
  }
  
  /**
   * Enviar logs a servicio externo (placeholder)
   */
  private static async logToExternalService(entry: WebhookLogEntry): Promise<void> {
    // Implementar integración con servicios como:
    // - DataDog: https://docs.datadoghq.com/api/latest/logs/
    // - LogRocket: https://docs.logrocket.com/reference/api
    // - Sentry: https://docs.sentry.io/api/
    
    // Ejemplo con fetch genérico:
    /*
    try {
      await fetch(process.env.EXTERNAL_LOGGING_ENDPOINT!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.LOGGING_API_KEY}`
        },
        body: JSON.stringify({
          service: 'oposiai-webhooks',
          level: entry.success ? 'info' : 'error',
          message: entry.message,
          metadata: entry
        })
      });
    } catch (error) {
      console.error('Failed to send log to external service:', error);
    }
    */
  }
  
  /**
   * Enviar alerta crítica (placeholder)
   */
  private static async sendCriticalAlert(errorLog: any): Promise<void> {
    // Implementar alertas críticas via:
    // - Slack webhook
    // - Email
    // - SMS
    // - PagerDuty
    
    // Ejemplo con Slack:
    /*
    try {
      await fetch(process.env.SLACK_WEBHOOK_URL!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 Critical Error in OposiAI`,
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*Context:* ${errorLog.context}\n*Error:* ${errorLog.message}\n*Time:* ${errorLog.timestamp}`
              }
            }
          ]
        })
      });
    } catch (error) {
      console.error('Failed to send critical alert:', error);
    }
    */
  }
  
  /**
   * Enviar métricas (placeholder)
   */
  private static sendMetrics(metrics: any): void {
    // Implementar envío de métricas a:
    // - DataDog
    // - New Relic
    // - CloudWatch
    // - Prometheus
    
    // Por ahora solo log local
    if (process.env.ENABLE_METRICS_LOGGING === 'true') {
      console.log('📈 [METRICS_EXPORT]', metrics);
    }
  }
  
  /**
   * Obtener estadísticas de webhooks
   */
  static async getWebhookStats(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<{
    totalEvents: number;
    successRate: number;
    averageProcessingTime: number;
    errorsByType: Record<string, number>;
  }> {
    // Esta función requeriría una tabla de logs en la BD
    // Por ahora retornamos estructura básica
    return {
      totalEvents: 0,
      successRate: 0,
      averageProcessingTime: 0,
      errorsByType: {}
    };
  }
}
