// src/lib/utils/planLimits.ts
// Configuración centralizada de límites y características por plan

export interface PlanLimits {
  documents: number; // -1 = ilimitado
  mindMapsPerWeek: number;
  testsPerWeek: number;
  flashcardsPerWeek: number;
  monthlyTokens: number;
  features: string[];
}

export interface PlanConfiguration {
  id: string;
  name: string;
  price: number; // en centavos
  limits: PlanLimits;
  features: string[];
  restrictedFeatures: string[];
}

// Configuración completa de planes con límites y características
export const PLAN_CONFIGURATIONS: Record<string, PlanConfiguration> = {
  free: {
    id: 'free',
    name: 'Plan Gratis',
    price: 0,
    limits: {
      documents: 1,
      mindMapsPerWeek: 2,
      testsPerWeek: 10,
      flashcardsPerWeek: 10,
      monthlyTokens: 50000,
      features: ['document_upload', 'test_generation', 'flashcard_generation', 'mind_map_generation']
    },
    features: [
      'document_upload',
      'test_generation',
      'flashcard_generation', 
      'mind_map_generation'
    ],
    restrictedFeatures: [
      'study_planning',
      'ai_tutor_chat',
      'summary_a1_a2'
    ]
  },
  usuario: {
    id: 'usuario',
    name: 'Plan Usuario',
    price: 1000, // €10.00
    limits: {
      documents: -1, // ilimitado
      mindMapsPerWeek: -1,
      testsPerWeek: -1,
      flashcardsPerWeek: -1,
      monthlyTokens: 1000000,
      features: ['document_upload', 'ai_tutor_chat', 'test_generation', 'flashcard_generation', 'mind_map_generation']
    },
    features: [
      'document_upload',
      'ai_tutor_chat',
      'test_generation',
      'flashcard_generation',
      'mind_map_generation'
    ],
    restrictedFeatures: [
      'study_planning',
      'summary_a1_a2'
    ]
  },
  pro: {
    id: 'pro',
    name: 'Plan Pro',
    price: 1500, // €15.00
    limits: {
      documents: -1, // ilimitado
      mindMapsPerWeek: -1,
      testsPerWeek: -1,
      flashcardsPerWeek: -1,
      monthlyTokens: 1000000,
      features: ['document_upload', 'study_planning', 'ai_tutor_chat', 'test_generation', 'flashcard_generation', 'mind_map_generation', 'summary_a1_a2']
    },
    features: [
      'document_upload',
      'study_planning',
      'ai_tutor_chat',
      'test_generation',
      'flashcard_generation',
      'mind_map_generation',
      'summary_a1_a2'
    ],
    restrictedFeatures: []
  }
};

// Funciones de utilidad
export function getPlanConfiguration(planId: string): PlanConfiguration | null {
  return PLAN_CONFIGURATIONS[planId] || null;
}

export function getTokenLimitForPlan(planId: string): number {
  const config = getPlanConfiguration(planId);
  return config?.limits.monthlyTokens || 50000;
}

export function hasFeatureAccess(planId: string, feature: string): boolean {
  const config = getPlanConfiguration(planId);
  if (!config) return false;
  
  return config.features.includes(feature) && !config.restrictedFeatures.includes(feature);
}

export function getWeeklyLimit(planId: string, limitType: 'mindMaps' | 'tests' | 'flashcards'): number {
  const config = getPlanConfiguration(planId);
  if (!config) return 0;
  
  switch (limitType) {
    case 'mindMaps':
      return config.limits.mindMapsPerWeek;
    case 'tests':
      return config.limits.testsPerWeek;
    case 'flashcards':
      return config.limits.flashcardsPerWeek;
    default:
      return 0;
  }
}

export function isUnlimited(limit: number): boolean {
  return limit === -1;
}

// Validar si un usuario puede realizar una acción específica
export function canPerformAction(
  planId: string,
  feature: string,
  currentUsage: number,
  limitType?: 'mindMaps' | 'tests' | 'flashcards'
): { allowed: boolean; reason?: string } {
  const config = getPlanConfiguration(planId);
  
  if (!config) {
    return { allowed: false, reason: 'Plan no válido' };
  }
  
  // Verificar si tiene acceso a la característica
  if (!hasFeatureAccess(planId, feature)) {
    return { allowed: false, reason: `Característica ${feature} no disponible en ${config.name}` };
  }
  
  // Verificar límites semanales si aplica
  if (limitType) {
    const weeklyLimit = getWeeklyLimit(planId, limitType);
    if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {
      return { allowed: false, reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})` };
    }
  }
  
  return { allowed: true };
}
