// src/lib/services/permissionService.ts
// Sistema centralizado de verificación de permisos

import { createClient } from '@/lib/supabase/supabaseClient';
import { SupabaseAdminService } from '@/lib/supabase/admin';
import { getPlanConfiguration, hasFeatureAccess } from '@/lib/utils/planLimits';
import { WebhookLogger } from '@/lib/utils/webhookLogger';

export interface PermissionCheck {
  feature: string;
  tokensRequired?: number;
  minimumPlan?: string[];
  requiresPayment?: boolean;
}

export interface PermissionResult {
  granted: boolean;
  reason?: string;
  userPlan?: string;
  requiredPlan?: string[];
  tokenInfo?: {
    current: number;
    limit: number;
    remaining: number;
    percentage: number;
  };
  upgradeRequired?: boolean;
  suggestedPlan?: string;
}

export class PermissionService {
  
  /**
   * Verificar permisos completos para una acción
   */
  static async checkPermission(
    userId: string,
    permission: PermissionCheck
  ): Promise<PermissionResult> {
    try {
      // Obtener perfil del usuario
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return {
          granted: false,
          reason: 'Perfil de usuario no encontrado'
        };
      }

      // Verificar pago para planes de pago
      if (permission.requiresPayment !== false && 
          profile.subscription_plan !== 'free' && 
          !profile.payment_verified) {
        return {
          granted: false,
          reason: 'Pago no verificado',
          userPlan: profile.subscription_plan,
          upgradeRequired: false
        };
      }

      // Verificar plan mínimo requerido
      if (permission.minimumPlan && permission.minimumPlan.length > 0) {
        if (!permission.minimumPlan.includes(profile.subscription_plan)) {
          return {
            granted: false,
            reason: `Esta función requiere plan ${permission.minimumPlan.join(' o ')}`,
            userPlan: profile.subscription_plan,
            requiredPlan: permission.minimumPlan,
            upgradeRequired: true,
            suggestedPlan: this.getSuggestedPlan(profile.subscription_plan, permission.minimumPlan)
          };
        }
      }

      // Verificar acceso a la característica
      if (!hasFeatureAccess(profile.subscription_plan, permission.feature)) {
        const planConfig = getPlanConfiguration(profile.subscription_plan);
        const requiredPlans = this.getPlansWithFeature(permission.feature);
        
        return {
          granted: false,
          reason: `La función ${permission.feature} no está disponible en ${planConfig?.name || profile.subscription_plan}`,
          userPlan: profile.subscription_plan,
          requiredPlan: requiredPlans,
          upgradeRequired: true,
          suggestedPlan: requiredPlans[0]
        };
      }

      // Verificar límites de tokens si se especifican
      if (permission.tokensRequired && permission.tokensRequired > 0) {
        const tokenCheck = await this.checkTokenLimits(profile, permission.tokensRequired);
        
        if (!tokenCheck.allowed) {
          return {
            granted: false,
            reason: tokenCheck.reason,
            userPlan: profile.subscription_plan,
            tokenInfo: tokenCheck.tokenInfo,
            upgradeRequired: tokenCheck.upgradeRequired,
            suggestedPlan: tokenCheck.suggestedPlan
          };
        }
      }

      // Si llegamos aquí, el permiso está concedido
      const tokenInfo = this.getTokenInfo(profile);
      
      // Log del acceso concedido
      await WebhookLogger.logFeatureAccess(
        userId,
        permission.feature,
        true,
        profile.subscription_plan,
        permission.tokensRequired || 0
      );

      return {
        granted: true,
        userPlan: profile.subscription_plan,
        tokenInfo
      };

    } catch (error) {
      console.error('Error checking permission:', error);
      
      // Log del error
      await WebhookLogger.logFeatureAccess(
        userId,
        permission.feature,
        false,
        'error',
        0,
        'Internal permission check error'
      );

      return {
        granted: false,
        reason: 'Error interno de verificación de permisos'
      };
    }
  }

  /**
   * Verificar múltiples permisos a la vez
   */
  static async checkMultiplePermissions(
    userId: string,
    permissions: PermissionCheck[]
  ): Promise<Record<string, PermissionResult>> {
    const results: Record<string, PermissionResult> = {};
    
    for (const permission of permissions) {
      results[permission.feature] = await this.checkPermission(userId, permission);
    }
    
    return results;
  }

  /**
   * Verificar límites de tokens
   */
  private static async checkTokenLimits(
    profile: any,
    tokensRequired: number
  ): Promise<{
    allowed: boolean;
    reason?: string;
    tokenInfo?: any;
    upgradeRequired?: boolean;
    suggestedPlan?: string;
  }> {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    
    // Reset si es nuevo mes
    let currentTokens = profile.current_month === currentMonth 
      ? profile.current_month_tokens 
      : 0;

    const tokenInfo = {
      current: currentTokens,
      limit: profile.monthly_token_limit,
      remaining: profile.monthly_token_limit - currentTokens,
      percentage: Math.round((currentTokens / profile.monthly_token_limit) * 100)
    };

    // Verificar si tiene tokens suficientes
    if (currentTokens + tokensRequired > profile.monthly_token_limit) {
      const suggestedPlan = profile.subscription_plan === 'free' ? 'usuario' : 'pro';
      
      return {
        allowed: false,
        reason: `Límite mensual de tokens alcanzado. Necesitas ${tokensRequired} tokens pero solo tienes ${tokenInfo.remaining} disponibles.`,
        tokenInfo,
        upgradeRequired: true,
        suggestedPlan
      };
    }

    return {
      allowed: true,
      tokenInfo
    };
  }

  /**
   * Obtener información de tokens del perfil
   */
  private static getTokenInfo(profile: any) {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    const currentTokens = profile.current_month === currentMonth 
      ? profile.current_month_tokens 
      : 0;

    return {
      current: currentTokens,
      limit: profile.monthly_token_limit,
      remaining: profile.monthly_token_limit - currentTokens,
      percentage: Math.round((currentTokens / profile.monthly_token_limit) * 100)
    };
  }

  /**
   * Obtener plan sugerido para upgrade
   */
  private static getSuggestedPlan(currentPlan: string, requiredPlans: string[]): string {
    // Si el usuario tiene plan gratuito, sugerir el plan más bajo requerido
    if (currentPlan === 'free') {
      return requiredPlans[0];
    }
    
    // Si el usuario tiene plan usuario y se requiere pro, sugerir pro
    if (currentPlan === 'usuario' && requiredPlans.includes('pro')) {
      return 'pro';
    }
    
    // Por defecto, sugerir el plan más alto
    return requiredPlans[requiredPlans.length - 1];
  }

  /**
   * Obtener planes que tienen acceso a una característica
   */
  private static getPlansWithFeature(feature: string): string[] {
    const plans = ['free', 'usuario', 'pro'];
    const plansWithFeature: string[] = [];
    
    for (const plan of plans) {
      if (hasFeatureAccess(plan, feature)) {
        plansWithFeature.push(plan);
      }
    }
    
    return plansWithFeature;
  }

  /**
   * Verificar permisos desde el cliente (React)
   */
  static async checkClientPermission(permission: PermissionCheck): Promise<PermissionResult> {
    try {
      const supabase = createClient();
      
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        return {
          granted: false,
          reason: 'Usuario no autenticado'
        };
      }

      return await this.checkPermission(user.id, permission);

    } catch (error) {
      console.error('Error checking client permission:', error);
      return {
        granted: false,
        reason: 'Error de verificación'
      };
    }
  }

  /**
   * Crear verificación de permisos para características comunes
   */
  static createFeaturePermission(
    feature: string,
    tokensRequired: number = 0
  ): PermissionCheck {
    const featureRequirements: Record<string, Partial<PermissionCheck>> = {
      'test_generation': { minimumPlan: ['free', 'usuario', 'pro'] },
      'flashcard_generation': { minimumPlan: ['free', 'usuario', 'pro'] },
      'mind_map_generation': { minimumPlan: ['free', 'usuario', 'pro'] },
      'ai_tutor_chat': { minimumPlan: ['usuario', 'pro'] },
      'study_planning': { minimumPlan: ['pro'] },
      'summary_a1_a2': { minimumPlan: ['pro'] },
      'document_upload': { minimumPlan: ['free', 'usuario', 'pro'] }
    };

    const requirements = featureRequirements[feature] || {};

    return {
      feature,
      tokensRequired,
      requiresPayment: true,
      ...requirements
    };
  }
}
